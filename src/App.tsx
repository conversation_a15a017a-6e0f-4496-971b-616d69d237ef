import { RouterProvider, createBrowserRouter } from 'react-router-dom';
import { Toaster } from 'sonner';
import { HelmetProvider } from 'react-helmet-async';
import { AdminPage } from './admin-page/AdminPage';
import { AdminTagsPage } from './admin-page/AdminTagsPage';
import { AdminAuthorsPage } from './admin-page/AdminAuthorsPage';
import { AdminResourcesPage } from './admin-page/AdminResourcesPage';
import { AdminProposalsPage } from './admin-page/AdminProposalsPage';
import { AdminBulkImportPage } from './admin-page/AdminBulkImportPage';
import { RootProvider } from './context/RootContext';
import { AdminCountersProvider } from './context/AdminCountersContext';
import ErrorPage from './error-page/ErrorPage';
import { useEffect, useState } from 'react';
import { useDarkMode } from 'usehooks-ts';
import { SearchFiltersContextProvider } from './search-page/search-filters-context';
import { AppResourcesProvider } from './search-page/app-resources-context';
import { SearchPage } from './search-page/search-page';

const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <SearchFiltersContextProvider>
        <AppResourcesProvider>
          <div className='h-screen flex flex-col'>
            <SearchPage />
          </div>
        </AppResourcesProvider>
      </SearchFiltersContextProvider>
    ),
  },
  {
    path: '/admin',
    element: (
      <SearchFiltersContextProvider>
        <AppResourcesProvider>
          <AdminCountersProvider>
            <AdminPage />
          </AdminCountersProvider>
        </AppResourcesProvider>
      </SearchFiltersContextProvider>
    ),
    errorElement: <ErrorPage />,
  },
  {
    path: '/admin/tags',
    element: (
      <SearchFiltersContextProvider>
        <AppResourcesProvider>
          <AdminCountersProvider>
            <AdminTagsPage />
          </AdminCountersProvider>
        </AppResourcesProvider>
      </SearchFiltersContextProvider>
    ),
    errorElement: <ErrorPage />,
  },
  {
    path: '/admin/authors',
    element: (
      <SearchFiltersContextProvider>
        <AppResourcesProvider>
          <AdminCountersProvider>
            <AdminAuthorsPage />
          </AdminCountersProvider>
        </AppResourcesProvider>
      </SearchFiltersContextProvider>
    ),
    errorElement: <ErrorPage />,
  },
  {
    path: '/admin/resources',
    element: (
      <SearchFiltersContextProvider>
        <AppResourcesProvider>
          <AdminCountersProvider>
            <AdminResourcesPage />
          </AdminCountersProvider>
        </AppResourcesProvider>
      </SearchFiltersContextProvider>
    ),
    errorElement: <ErrorPage />,
  },
  {
    path: '/admin/proposals',
    element: (
      <SearchFiltersContextProvider>
        <AppResourcesProvider>
          <AdminCountersProvider>
            <AdminProposalsPage />
          </AdminCountersProvider>
        </AppResourcesProvider>
      </SearchFiltersContextProvider>
    ),
    errorElement: <ErrorPage />,
  },
  {
    path: '/admin/bulk-import',
    element: (
      <SearchFiltersContextProvider>
        <AppResourcesProvider>
          <AdminCountersProvider>
            <AdminBulkImportPage />
          </AdminCountersProvider>
        </AppResourcesProvider>
      </SearchFiltersContextProvider>
    ),
    errorElement: <ErrorPage />,
  },
]);

function App() {
  const { isDarkMode } = useDarkMode(true);
  const [restricted, setRestricted] = useState(false);

  useEffect(() => {
    fetch('https://ipapi.co/json/')
      .then((response) => response.json())
      .then((data) => {
        console.log(data);
        if (['RU', 'IR', 'KP', 'CN'].includes(data.country)) {
          setRestricted(true);
        }
      })
      .catch((error) => {
        console.log(error);
      });
  }, []);

  useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
      document.body.classList.add('bodyDark');
    } else {
      document.documentElement.classList.remove('dark');
      document.body.classList.remove('bodyDark');
    }
  }, [isDarkMode]);

  if (restricted) {
    return (
      <div className='fixed inset-0 flex justify-center items-center'>
        russia is a terrorist state
      </div>
    );
  }

  return (
    <HelmetProvider>
      <RootProvider>
        <RouterProvider router={router} />
        <Toaster />
      </RootProvider>
    </HelmetProvider>
  );
}

export default App;
